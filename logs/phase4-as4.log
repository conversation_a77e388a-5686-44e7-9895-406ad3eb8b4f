2025-07-02 10:29:07.785 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 4476 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:29:07.910 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:29:07.910 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:29:10.553 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:29:10.553 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:29:16.123 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:29:16.124 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.127 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.127 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.128 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.161 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:29:16.161 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.162 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.162 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.162 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.170 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:29:16.171 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:29:16.176 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:29:16.177 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.177 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.207 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:29:16.207 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:29:16.207 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:29:16.208 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:29:16.208 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:29:16.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:29:16.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:29:16.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:29:16.298 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:29:16.299 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:29:16.299 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:29:16.300 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:29:16.329 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:29:16.330 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:29:16.430 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:29:16.431 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:29:16.432 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.432 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.432 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.433 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:29:16.433 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:29:16.433 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:29:16.433 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:29:16.433 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:29:16.433 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:29:16.434 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:29:16.434 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:29:16.434 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:29:16.434 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:29:16.435 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:29:16.435 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.436 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:29:16.437 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.438 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.438 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:29:16.438 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.439 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.444 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:29:16.444 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:29:16.444 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.445 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.445 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:29:16.477 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:29:16.477 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:29:16.477 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.477 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.492 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:29:16.492 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.492 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.492 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:29:16.654 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:29:16.654 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:29:17.000 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:29:17.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:29:17.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:29:17.555 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 10.724 seconds (process running for 11.453)
2025-07-02 10:29:17.555 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:29:17.569 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:29:17.679 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:29:17.683 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:29:17.684 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:29:17.685 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:29:17.686 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:29:17.689 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:29:17.691 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:29:17.695 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:29:17.696 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:29:17.743 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:29:17.743 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:29:38.600 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing request for country: DEFAULT and document type: INVOICE
2025-07-02 10:29:38.602 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 10:29:39.993 [http-nio-8081-exec-1] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 10:29:39.994 [http-nio-8081-exec-1] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 10:29:39.995 [http-nio-8081-exec-1] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 10:29:39.996 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:29:39.997 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:29:40.033 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-07-02 10:29:40.117 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-07-02 10:29:40.119 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-07-02 10:29:40.119 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:29:40.119 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:29:40.917 [http-nio-8081-exec-1] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 10:29:40.917 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 10:29:40.997 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:29:40.997 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:29:41.002 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:29:41.003 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:29:41.003 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:29:41.006 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:29:41.006 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 10:29:41.007 [http-nio-8081-exec-1] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 10:29:47.692 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:29:47.704 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:29:47.705 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:29:47.706 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:29:47.712 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:29:53.787 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:29:53.787 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 10:30:01.783 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 16200 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:30:01.787 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:30:01.787 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:30:05.470 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:30:05.470 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:30:11.033 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:30:11.033 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.033 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.033 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.033 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.080 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:30:11.080 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.080 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.080 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.080 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.087 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:30:11.087 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:30:11.087 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:30:11.087 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.087 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:30:11.122 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:30:11.122 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:30:11.122 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:30:11.220 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:30:11.220 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:30:11.220 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:30:11.220 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:30:11.246 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:30:11.246 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.365 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:30:11.365 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:30:11.365 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.365 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.365 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:30:11.407 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:30:11.407 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:30:11.407 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.407 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.422 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:30:11.422 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.422 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.422 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:30:11.589 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:30:11.589 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:30:11.904 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:30:12.530 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:30:12.530 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:30:12.550 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:30:12.550 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 12.933 seconds (process running for 15.093)
2025-07-02 10:30:12.566 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:30:12.719 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:30:12.719 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:30:12.737 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:30:12.737 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:30:12.737 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:30:12.737 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:30:12.737 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:30:12.737 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:30:12.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:30:12.759 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:30:12.806 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:30:12.806 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:30:12.806 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:30:12.806 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:30:29.032 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing request for country: DEFAULT and document type: INVOICE
2025-07-02 10:30:29.034 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 10:30:29.918 [http-nio-8081-exec-1] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 10:30:29.919 [http-nio-8081-exec-1] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 10:30:29.920 [http-nio-8081-exec-1] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 10:30:29.922 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:30:29.922 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:30:29.967 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-07-02 10:30:30.056 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-07-02 10:30:30.057 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-07-02 10:30:30.058 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:30:30.058 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:30:30.892 [http-nio-8081-exec-1] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 10:30:30.893 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 10:30:30.911 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:30:30.911 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:30:30.933 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:30:30.933 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:30:30.933 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:30:30.936 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:30:30.937 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 10:30:30.937 [http-nio-8081-exec-1] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 10:32:00.705 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:32:00.705 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m53s893ms803µs100ns).
2025-07-02 10:32:00.714 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:32:00.716 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:32:00.716 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:32:00.720 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:32:11.898 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:32:11.914 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 10:32:18.711 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 25488 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:32:18.711 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:32:18.711 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:32:21.800 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:32:21.801 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:32:27.574 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:32:27.589 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.589 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.589 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.589 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.626 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:32:27.626 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.626 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.626 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.626 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.641 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:32:27.641 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:32:27.657 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:32:27.657 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.657 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:32:27.704 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:32:27.704 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:32:27.704 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:32:27.783 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:32:27.783 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:32:27.783 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:32:27.783 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:32:27.827 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:32:27.827 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:32:27.941 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.941 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:32:27.941 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.941 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.957 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:32:27.957 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:32:27.957 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.957 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.957 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:32:27.988 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:32:27.988 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:32:28.004 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:28.004 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:28.004 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:32:28.004 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:28.004 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:28.004 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:32:28.201 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:32:28.201 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:32:28.579 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:32:29.132 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:32:29.132 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:32:29.163 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 11.637 seconds (process running for 12.631)
2025-07-02 10:32:29.163 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:32:29.163 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:32:29.302 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:32:29.320 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:32:29.320 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:32:29.320 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:32:29.320 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:32:29.327 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:32:29.374 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:32:29.374 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:32:37.444 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing request for country: DEFAULT and document type: INVOICE
2025-07-02 10:32:37.448 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 10:32:38.308 [http-nio-8081-exec-2] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 10:32:38.308 [http-nio-8081-exec-2] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 10:32:38.309 [http-nio-8081-exec-2] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 10:32:38.311 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:32:38.311 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:32:38.362 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-07-02 10:32:38.448 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-07-02 10:32:38.449 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-07-02 10:32:38.450 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:32:38.450 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:32:39.275 [http-nio-8081-exec-2] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 10:32:39.275 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 10:32:39.292 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:32:39.292 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:32:39.296 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:32:39.296 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:32:39.296 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:32:39.299 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:32:39.299 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 10:32:39.300 [http-nio-8081-exec-2] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 10:32:50.508 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:32:50.508 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 10:37:42.280 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 8308 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:37:42.280 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:37:42.280 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:37:45.009 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:37:45.009 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:37:49.646 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:37:49.646 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.646 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.646 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.646 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.678 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:37:49.678 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.678 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.678 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.678 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.693 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:37:49.693 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:37:49.693 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:37:49.693 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.693 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:37:49.724 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:37:49.724 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:37:49.724 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:37:49.803 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:37:49.803 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:37:49.803 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:37:49.803 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:37:49.818 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:37:49.818 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:37:49.926 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:37:49.927 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:37:49.928 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.928 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.928 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.928 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:37:49.928 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:37:49.929 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:37:49.929 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:37:49.929 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:37:49.929 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:37:49.929 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:37:49.930 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:37:49.930 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:37:49.931 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:37:49.931 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:37:49.931 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:37:49.931 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.931 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:37:49.933 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.934 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.934 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:37:49.935 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.935 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.939 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:37:49.939 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:37:49.939 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.939 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.939 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:37:49.972 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:37:49.972 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:37:49.972 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.972 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.972 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:37:49.972 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.972 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.972 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:37:50.153 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:37:50.153 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:37:50.454 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:37:50.970 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:37:50.970 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:37:50.986 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 9.649 seconds (process running for 10.355)
2025-07-02 10:37:50.986 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:37:50.986 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:37:51.123 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:37:51.125 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:37:51.126 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:37:51.128 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:37:51.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:37:51.131 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:37:51.133 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:37:51.137 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:37:51.140 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:37:51.141 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:37:51.142 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:37:51.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:37:51.146 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:37:51.148 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:37:51.150 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:37:51.152 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:37:51.154 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:37:51.156 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:37:51.157 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:37:51.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:37:51.161 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:37:51.164 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:37:51.167 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:37:51.169 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:37:51.172 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:37:51.174 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:37:51.176 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:37:51.179 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:37:51.182 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:37:51.185 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:37:51.185 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:37:51.185 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:37:53.295 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing request for country: DEFAULT and document type: INVOICE
2025-07-02 10:37:53.297 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 10:37:53.452 [http-nio-8081-exec-1] ERROR c.m.a.a.c.PeppolSbdInvoiceController - Failed to log invoice generation result
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: JSON parse error: Cannot deserialize value of type `java.time.LocalDate` from String "UNKNOWN": Failed to deserialize java.time.LocalDate: (java.time.format.DateTimeParseException) Text 'UNKNOWN' could not be parsed at index 0"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:183)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 10:37:53.734 [http-nio-8081-exec-1] INFO  c.m.a.c.s.ErrorManagementService - Error logged: CONN_005 for message: null
2025-07-02 10:37:53.749 [http-nio-8081-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.morohub.apsp.config.GlobalExceptionHandler#handleConnectivityException(ConnectivityException)
java.lang.NullPointerException: Cannot invoke "java.util.Map.put(Object, Object)" because the return value of "com.morohub.apsp.common.dto.ErrorResponse.getAdditionalInfo()" is null
	at com.morohub.apsp.config.GlobalExceptionHandler.handleConnectivityException(GlobalExceptionHandler.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:432)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:74)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:161)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1357)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 10:37:53.752 [http-nio-8081-exec-1] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: ConnectivityException{errorCode='CONN_005', endpoint='http://localhost:8090/api/secure/decrypt-invoice/0235:1234567891', httpStatusCode=0, responseTime=0, message='Failed to connect to decryption service: 500 : "Error: Authorization failed due to wrong key"'}] with root cause
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: Authorization failed due to wrong key"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:108)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 10:38:21.132 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:38:21.132 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:38:21.132 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:38:21.132 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:38:21.147 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:39:21.172 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:39:21.172 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:39:21.172 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:39:21.172 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:39:21.188 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:39:51.184 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:39:51.184 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:39:51.184 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:39:51.184 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:39:51.200 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:40:21.200 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:40:21.201 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:40:21.201 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:40:21.201 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:40:21.218 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - Found 1 errors ready for retry
2025-07-02 10:40:21.218 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - 🔄 Processing retry for error log: 2 (attempt 1)
2025-07-02 10:40:21.233 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - No message ID found for error log 2, cannot retry
2025-07-02 10:40:21.233 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - ❌ Retry failed for error log: 2, scheduling next retry
2025-07-02 10:40:21.233 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - ✅ Processed 1 retry attempts
2025-07-02 10:40:51.213 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:40:51.213 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:40:51.213 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:40:51.259 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:40:51.259 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:41:14.817 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:41:14.817 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 10:41:19.358 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 26156 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:41:19.358 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:41:19.358 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:41:22.653 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:41:22.653 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:41:28.038 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:41:28.038 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.038 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.038 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.038 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.076 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:41:28.076 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.076 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.076 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.076 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.091 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:41:28.091 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:41:28.091 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:41:28.091 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.091 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:41:28.138 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:41:28.138 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:41:28.138 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:41:28.216 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:41:28.216 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:41:28.216 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:41:28.216 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:41:28.254 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:41:28.254 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.364 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:41:28.364 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:41:28.364 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.364 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.364 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:41:28.412 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:41:28.412 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:41:28.412 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.412 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.412 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:41:28.427 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.427 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.427 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:41:28.620 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:41:28.620 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:41:28.978 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:41:29.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:41:29.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:41:29.555 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 11.653 seconds (process running for 13.018)
2025-07-02 10:41:29.555 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:41:29.580 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:41:29.733 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:41:29.745 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:41:29.745 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:41:29.745 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:41:29.745 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:41:29.751 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:41:29.755 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:41:29.802 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:41:29.802 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:41:29.802 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:41:29.802 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:41:59.758 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:41:59.758 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:41:59.758 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:41:59.773 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:42:10.102 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:42:29.779 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:42:29.779 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:42:29.779 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:42:40.124 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:42:47.878 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - Found 1 errors ready for retry
2025-07-02 10:42:56.100 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - 🔄 Processing retry for error log: 2 (attempt 2)
2025-07-02 10:43:09.650 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - No message ID found for error log 2, cannot retry
2025-07-02 10:43:15.468 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - ❌ Retry failed for error log: 2, scheduling next retry
2025-07-02 10:44:07.701 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m13s737ms341µs900ns).
2025-07-02 10:44:07.723 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - ✅ Processed 1 retry attempts
2025-07-02 10:44:07.845 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:44:07.845 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
