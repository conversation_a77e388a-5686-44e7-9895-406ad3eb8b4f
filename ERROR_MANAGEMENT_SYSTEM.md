# AS4 Access Point Error Management System

## Overview

This document describes the comprehensive error management system implemented for the AS4 Access Point application. The system addresses all production requirements for error handling, retry mechanisms, and error reporting.

## Requirements Addressed

### Requirement 1: Master Data for All Errors
✅ **Implemented**: All errors (technical, schematron validation, and others) are maintained in the Access Point database with the following details:
- **Error Code** (HTTP error code, Data error code as defined by Xelerate etc.) - Mandatory
- **Error Type** (Technical Connectivity Error, Schema Validation Failure, Document Capability Mismatch, Others) - Mandatory  
- **Error Description** - Mandatory
- **Retry Required Y/N** - Mandatory
- **Retry Interval** - Mandatory if "Retry Required Y/N" is Y

### Requirement 2: Appropriate Error Codes and Descriptions
✅ **Implemented**: Access point API responses provide appropriate error codes and descriptions with different error codes for:
- Authentication failure (401)
- Schema validation errors (400)
- Schematron validation errors (400)
- Connectivity errors (503, 502, 504, etc.)
- System errors (500)

### Requirement 3: Multiple Errors in Single Response
✅ **Implemented**: For multiple errors in the same API request, all errors are provided in a single response using the `MultipleErrorResponse` DTO.

### Requirement 4: Automatic Retry and Manual Operations
✅ **Implemented**: 
- **Automatic Retry**: Configurable retry interval and number of retries for technical connectivity errors
- **Manual Operations**: API facilities to reprocess or discard errors that failed after maximum retries

## Architecture Components

### 1. Database Entities

#### ErrorMasterData
- Stores master data for all error types
- Includes retry configuration and error categorization
- Pre-populated with common error codes during application startup

#### ErrorLog  
- Tracks all error occurrences
- Supports retry scheduling and resolution tracking
- Links to ErrorMasterData for configuration

### 2. Exception Classes

#### AS4Exception (Base)
- Base exception class for all AS4-related errors
- Contains error code, type, severity, and retry information

#### ValidationException
- Handles validation errors (Schema, Schematron, Business Rules)
- Supports multiple validation errors in a single exception

#### ConnectivityException
- Handles technical connectivity errors
- Automatically marked as retryable based on error type

#### AuthenticationException
- Handles authentication and authorization failures
- Generally not retryable

### 3. Services

#### ErrorManagementService
- Central service for error logging and response generation
- Handles error master data lookup and error log creation

#### RetryService
- Manages automatic retry processing
- Provides manual retry and discard operations
- Scheduled processing of retry queue

### 4. Response DTOs

#### ErrorResponse
- Standardized error response for single errors
- Includes error code, description, retry information

#### MultipleErrorResponse
- Handles multiple errors in a single response
- Provides error summary and categorization

### 5. Global Exception Handler
- Centralized exception handling for all controllers
- Automatic error logging and response generation
- Converts exceptions to standardized error responses

## Configuration

### Application Properties
```properties
# Error Management Configuration
error.management.enabled=true
error.retry.enabled=true
error.retry.poll.interval.ms=30000
error.retry.batch.size=10
error.retry.max.concurrent=3

# Default Error Configuration
error.default.retry.interval.ms=60000
error.default.max.retry.attempts=3
error.default.timeout.ms=30000
```

## Pre-configured Error Codes

The system comes with pre-configured error codes for common scenarios:

### Authentication Errors
- `AUTH_001`: Invalid authentication credentials (401)
- `AUTH_002`: Authentication token expired (401)
- `AUTH_003`: Insufficient permissions (403)
- `AUTH_004`: Invalid or expired certificate (401)

### Validation Errors
- `VAL_001`: XML schema validation failed (400)
- `VAL_002`: Schematron validation failed (400)
- `VAL_003`: Business rule validation failed (400)
- `VAL_004`: Document type not supported (400)

### Connectivity Errors (Retryable)
- `CONN_001`: Service temporarily unavailable (503)
- `CONN_002`: Request timeout (408)
- `CONN_003`: Bad gateway (502)
- `CONN_004`: Gateway timeout (504)
- `CONN_005`: Connection refused (500)

### System Errors
- `SYS_001`: Internal server error (500)
- `SYS_002`: System configuration error (500)
- `SYS_003`: Insufficient storage space (507)

### AS4 Specific Errors
- `AS4_001`: AS4 message processing failed (500)
- `AS4_002`: Invalid AS4 message format (400)
- `AS4_003`: AS4 message encryption failed (500)
- `AS4_004`: AS4 message signature verification failed (500)

### Peppol Specific Errors
- `PEPPOL_001`: Participant not found in SMP (404)
- `PEPPOL_002`: Document type not supported by participant (400)
- `PEPPOL_003`: SMP lookup failed (500)

### MLS Specific Errors
- `MLS_001`: MLS message sending failed (500)
- `MLS_002`: Invalid MLS message format (400)

## API Endpoints

### Error Management APIs
- `GET /api/error-management/master-data/{errorCode}` - Get error master data
- `GET /api/error-management/logs/message/{messageId}` - Get error logs for message
- `GET /api/error-management/logs/unresolved` - Get unresolved errors
- `GET /api/error-management/logs/ready-for-retry` - Get errors ready for retry
- `POST /api/error-management/logs/{errorLogId}/resolve` - Mark error as resolved
- `POST /api/error-management/logs/{errorLogId}/retry` - Manual retry
- `POST /api/error-management/logs/{errorLogId}/discard` - Discard error
- `GET /api/error-management/statistics/retry` - Get retry statistics
- `GET /api/error-management/dashboard` - Get error dashboard
- `GET /api/error-management/health` - Health check

## Usage Examples

### 1. Throwing Validation Exception
```java
List<ValidationException.ValidationError> errors = new ArrayList<>();
errors.add(new ValidationException.ValidationError("field1", "Field is required"));
errors.add(new ValidationException.ValidationError("field2", "Invalid format"));

throw ValidationException.schematronValidation("VAL_002", 
    "Schematron validation failed", errors, messageId, requestId);
```

### 2. Throwing Connectivity Exception
```java
throw ConnectivityException.timeout("CONN_002", 
    "Request timeout", endpoint, responseTime, messageId, requestId);
```

### 3. Manual Retry via API
```bash
curl -X POST "/api/error-management/logs/123/retry?triggeredBy=admin"
```

### 4. Discard Error via API
```bash
curl -X POST "/api/error-management/logs/123/discard" \
  -d "discardedBy=admin&reason=Business decision to skip"
```

## Monitoring and Observability

### Retry Statistics
The system provides comprehensive retry statistics including:
- Total errors in last 24 hours
- Unresolved errors count
- Errors ready for retry
- Errors that exceeded max retries

### Error Dashboard
Centralized dashboard showing:
- Error counts by type and severity
- Retry queue status
- System health indicators

### Logging
All error operations are logged with appropriate log levels:
- `INFO`: Successful operations, retry attempts
- `WARN`: Retry failures, configuration issues
- `ERROR`: System errors, unexpected failures

## Database Schema

The system uses two main tables:
- `error_master_data`: Master configuration for all error types
- `error_log`: Tracking of error occurrences and retry attempts

Refer to `V001__Create_Error_Management_Tables.sql` for complete schema definition.

## Integration with Existing Code

The error management system integrates seamlessly with existing code through:
1. **Global Exception Handler**: Automatically catches and processes all exceptions
2. **Custom Exceptions**: Replace generic exceptions with typed AS4 exceptions
3. **Service Integration**: Error logging happens automatically through AOP or manual calls

## Production Deployment Checklist

- [ ] Database migration scripts executed
- [ ] Error master data initialized
- [ ] Retry service configuration verified
- [ ] Monitoring endpoints accessible
- [ ] Log levels configured appropriately
- [ ] Error notification system configured (if required)
- [ ] Performance testing of retry mechanisms completed
