package com.morohub.apsp.api.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.morohub.apsp.common.dto.PeppolReq;
import com.morohub.apsp.core.service.InvoiceService;
import com.morohub.apsp.config.CountryConfigurationService;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;

import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ValidationException;
import com.morohub.apsp.common.exception.ConnectivityException;
import com.morohub.apsp.core.entity.ErrorMasterData;
import oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;

@RestController
@RequestMapping("/invoices")
public class PeppolSbdInvoiceController {

	private static final Logger logger = LoggerFactory.getLogger(PeppolSbdInvoiceController.class);

    private final InvoiceService invoiceService;

    @Autowired
    Validator validator;
    
    @Value("${decrypt.api.host}")
    private String decryptHost;

  

    @Value("${decrypt.api.port}")
    private String decryptPort;
    
    @Value("${logging.api.port}")
    private String logPort;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private Environment env;

    @Autowired
    private ObjectMapper objectMapper; // For JSON parsing

    @Autowired
    private CountryConfigurationService countryConfigurationService;




    public PeppolSbdInvoiceController(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }



    @PostMapping(value = "/generateInvoice", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> sendPeppolSbdInvoice(
            @RequestBody PeppolReq req) {

        String status = "SUCCESS";
        String failureReason = "N/A";
        Object request = null;
        ResponseEntity<JsonNode> response=null;

        try {
            // Step 1: Validate country and document type
            String countryCode = req.getCountryCode() != null ? req.getCountryCode() : "DEFAULT";
            String documentType = req.getDocumentType() != null ? req.getDocumentType() : "INVOICE";

            logger.info("Processing request for country: {} and document type: {}", countryCode, documentType);

            if (!countryConfigurationService.isSupported(countryCode, documentType)) {
                throw new ValidationException("VAL_004",
                    "Unsupported country/document type combination: " + countryCode + "/" + documentType,
                    "countryCode", "Unsupported combination");
            }

            // Step 2: Get dynamic class configuration
            Class<?> invoiceClass = countryConfigurationService.getUBLClass(countryCode, documentType);
            logger.info("Using UBL class: {} for {}/{}", invoiceClass.getName(), countryCode, documentType);

            // Step 3: Decrypt message
            String url = String.format("http://%s:%s/api/secure/decrypt-invoice/%s", decryptHost, decryptPort, req.getPeppolId());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(req.getReqMessage(), headers);


            try {
                response = restTemplate.postForEntity(url, entity, JsonNode.class);
            } catch (Exception e) {
                throw ConnectivityException.connectionRefused("CONN_005",
                    "Failed to connect to decryption service: " + e.getMessage(),
                    url, null, java.util.UUID.randomUUID().toString(), e);
            }

            // Step 4: Convert JSON to DTO using dynamic class
            request = objectMapper.treeToValue(response.getBody(), invoiceClass);

            // Step 5: Validate
            Set<ConstraintViolation<Object>> violations = validator.validate(request);
            if (!violations.isEmpty()) {
                List<ValidationException.ValidationError> validationErrors = new ArrayList<>();
                for (ConstraintViolation<Object> violation : violations) {
                    validationErrors.add(new ValidationException.ValidationError(
                        violation.getPropertyPath().toString(),
                        violation.getMessage()));
                }

                throw ValidationException.schemaValidation("VAL_001",
                    "Request validation failed", validationErrors, null, java.util.UUID.randomUUID().toString());
            }

            // Step 6: Generate invoice using country-specific configuration
            String responseXml = invoiceService.generateInvoiceWithConfig(request, countryCode, documentType);
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_XML)
                    .body(responseXml);

        }
        catch (ValidationException e) {
            // Re-throw custom exceptions to be handled by GlobalExceptionHandler
            throw e;
        } catch (ConnectivityException e) {
            // Re-throw custom exceptions to be handled by GlobalExceptionHandler
            throw e;
        }catch (AS4Exception e) {
            // Re-throw custom exceptions to be handled by GlobalExceptionHandler
            throw e;
        }  catch (Exception e) {
            status = "FAILED";
            failureReason = e.getMessage();
            logger.error("❌ Unexpected error in generateInvoice: {}", e.getMessage(), e);

            // Convert to AS4Exception for error management
            throw new AS4Exception("SYS_001", "Invoice generation failed: " + e.getMessage(),
                ErrorMasterData.ErrorType.SYSTEM_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, false, null, java.util.UUID.randomUUID().toString(), e);
        } finally {
            try {
                // Step 5: Prepare log data
                ObjectNode logPayload = objectMapper.createObjectNode();
                logPayload.put("peppolId",  req.getPeppolId());
                JsonNode body = response != null ? response.getBody() : null;

                logPayload.put("invoiceNumber",
                        body != null && body.has("id") && body.get("id").has("value")
                                ? body.get("id").get("value").asText()
                                : "UNKNOWN");

                logPayload.put("invoiceDate",
                        body != null && body.has("issueDate") && body.get("issueDate").has("value")
                                ? body.get("issueDate").get("value").asText()
                                : "UNKNOWN");
                logPayload.put("processDate", LocalDate.now().toString());
                logPayload.put("status", status);
                logPayload.put("failureReason", failureReason);

                // Step 6: Send log
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> entity = new HttpEntity<>(logPayload.toString(), headers);

                String logUrl = String.format("http://%s:%s/api/invoices/create", decryptHost, logPort);
                restTemplate.postForEntity(logUrl, entity, Void.class);

            } catch (Exception logEx) {
                logger.error("Failed to log invoice generation result", logEx);
            }
        }
    }


}

