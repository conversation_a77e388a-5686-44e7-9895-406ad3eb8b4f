package com.morohub.apsp.core.service;

import java.io.ByteArrayInputStream;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;

import com.morohub.apsp.config.AS4DecryptHelper;
import com.morohub.apsp.core.entity.MessageProcessingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.morohub.apsp.config.AS4CryptoConfiguration;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import javax.xml.XMLConstants;
import javax.xml.namespace.NamespaceContext;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;

import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ValidationException;
import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Phase4-compatible AS4 message receiver service
 * Processes AS4 messages and extracts UBL documents for further processing
 */
@Service
public class Phase4AS4ReceiverService {

    private static final Logger logger = LoggerFactory.getLogger(Phase4AS4ReceiverService.class);

    @Autowired
    private AS4CryptoConfiguration cryptoConfiguration;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private XmlJsonConversionService xmlJsonConversionService;

    @Autowired
    private  AS4DecryptHelper as4DecryptHelper;

    @Autowired
    private MessageQueueService messageQueueService;



    /**
     * Process incoming AS4 message content
     * This method handles AS4 messages and extracts UBL documents for processing
     * Note: For now, this is a simplified implementation. Full AS4 decryption can be added later.
     */
    public String processIncomingAS4MessageContent(String as4MessageContent, byte[] as4MessageBytes) throws Exception {
        try {
            logger.info("=== Processing AS4 message content ===");
            logger.info("📦 AS4 message length: {}", as4MessageContent.length());

            String ublXml = as4DecryptHelper.testAS4Decryption(as4MessageBytes);
            logger.debug("🔍 AS4 message preview with header: {}", ublXml);

            // Extract the invoice document from SBDH-wrapped UBL XML if needed
            logger.debug("🔍 Original UBL XML preview: {}", ublXml.substring(0, Math.min(1000, ublXml.length())));
            String cleanUblXml = extractInvoiceFromSbdhWrappedUbl(ublXml);
            logger.info("📄 Extracted clean UBL document, length: {}", cleanUblXml.length());
            logger.debug("🔍 Clean UBL XML preview: {}", cleanUblXml.substring(0, Math.min(1000, cleanUblXml.length())));

            String countryCode = extractCountryCodeFromXml(cleanUblXml);
            String documentType = extractDocumentTypeFromXml(cleanUblXml);

            logger.info("🌍 Detected country: {}, document type: {}", countryCode, documentType);

        // Process the UBL document using the invoice service with auto-detected configuration
        String result = invoiceService.processIncomingMessageWithCountryConfig(cleanUblXml, countryCode, documentType);

        // Extract SBDH information for MLS processing
        SbdhInfo sbdhInfo = extractSbdhInfo(ublXml);

        // Insert the UBL XML into the message processing queue with SBDH info
        try {
            MessageProcessingQueue message = messageQueueService.addMessageToQueue(cleanUblXml, countryCode, documentType,sbdhInfo);



            logger.info("📋 UBL XML added to message processing queue for country: {}, document type: {}", countryCode, documentType);
        } catch (Exception e) {
            logger.error("❌ Failed to add UBL XML to message processing queue: {}", e.getMessage(), e);

            // Throw AS4Exception for queue insertion failures
            throw new AS4Exception("AS4_001", "Failed to add message to processing queue: " + e.getMessage(),
                ErrorMasterData.ErrorType.SYSTEM_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, true, null, null, e);
        }

            logger.info("✅ AS4 message processed successfully");
            return result;

        } catch (AS4Exception e) {
            logger.error("❌ AS4 error processing incoming message: {}", e.getMessage(), e);
            throw e; // Re-throw for error management system
        } catch (ValidationException e) {
            logger.error("❌ Validation error processing incoming message: {}", e.getMessage(), e);
            throw e; // Re-throw for error management system
        } catch (Exception e) {
            logger.error("❌ Unexpected error processing incoming AS4 message: {}", e.getMessage(), e);

            // Convert to AS4Exception for error management
            throw new AS4Exception("AS4_002", "Failed to process incoming AS4 message: " + e.getMessage(),
                ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, true, null, null, e);
        }
    }

    /**
     * Extract the invoice document from SBDH-wrapped UBL XML
     * Removes SBDH headers if present and returns the clean UBL document
     */
    private String extractInvoiceFromSbdhWrappedUbl(String ublXml) {
        try {
            logger.debug("Checking if UBL XML contains SBDH headers");

            javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
            org.w3c.dom.Document doc = builder.parse(new java.io.ByteArrayInputStream(ublXml.getBytes(StandardCharsets.UTF_8)));

            String rootElementName = doc.getDocumentElement().getLocalName();
            if (rootElementName == null) {
                rootElementName = doc.getDocumentElement().getNodeName();
            }

            // Check if this is an SBDH-wrapped document
            if ("StandardBusinessDocument".equals(rootElementName)) {
                logger.info("📋 SBDH headers detected, extracting invoice document");
                logger.debug("🔍 Root element namespace: {}", doc.getDocumentElement().getNamespaceURI());

                // Look for UBL document elements within the SBDH
                org.w3c.dom.NodeList invoiceNodes = doc.getElementsByTagNameNS("*", "Invoice");
                logger.debug("🔍 Found {} Invoice nodes", invoiceNodes.getLength());

                if (invoiceNodes.getLength() > 0) {
                    org.w3c.dom.Node invoiceNode = invoiceNodes.item(0);
                    logger.debug("🔍 Invoice node namespace: {}, local name: {}",
                        invoiceNode.getNamespaceURI(), invoiceNode.getLocalName());

                    String extractedXml = nodeToString(invoiceNode);
                    logger.info("📄 Extracted Invoice document from SBDH, length: {}", extractedXml.length());
                    return extractedXml;
                }

                org.w3c.dom.NodeList creditNoteNodes = doc.getElementsByTagNameNS("*", "CreditNote");
                logger.debug("🔍 Found {} CreditNote nodes", creditNoteNodes.getLength());

                if (creditNoteNodes.getLength() > 0) {
                    org.w3c.dom.Node creditNoteNode = creditNoteNodes.item(0);
                    logger.debug("🔍 CreditNote node namespace: {}, local name: {}",
                        creditNoteNode.getNamespaceURI(), creditNoteNode.getLocalName());

                    String extractedXml = nodeToString(creditNoteNode);
                    logger.info("📄 Extracted CreditNote document from SBDH, length: {}", extractedXml.length());
                    return extractedXml;
                }

                org.w3c.dom.NodeList applicationResponseNodes = doc.getElementsByTagNameNS("*", "ApplicationResponse");
                logger.debug("🔍 Found {} ApplicationResponse nodes", applicationResponseNodes.getLength());

                if (applicationResponseNodes.getLength() > 0) {
                    org.w3c.dom.Node appResponseNode = applicationResponseNodes.item(0);
                    logger.debug("🔍 ApplicationResponse node namespace: {}, local name: {}",
                        appResponseNode.getNamespaceURI(), appResponseNode.getLocalName());

                    String extractedXml = nodeToString(appResponseNode);
                    logger.info("📄 Extracted ApplicationResponse document from SBDH, length: {}", extractedXml.length());
                    return extractedXml;
                }

                // Debug: List all child elements to see what's available
                logger.debug("🔍 Debugging SBDH structure - listing all child elements:");
                listChildElements(doc.getDocumentElement(), 0);

                logger.warn("⚠️ SBDH detected but no recognized UBL document found inside");
                return ublXml; // Return original if no UBL document found
            } else {
                logger.debug("✅ No SBDH headers detected, UBL document is already clean");
                return ublXml; // Return original if no SBDH wrapper
            }

        } catch (Exception e) {
            logger.error("❌ Error extracting invoice from SBDH-wrapped UBL: {}", e.getMessage());
            logger.warn("⚠️ Returning original UBL XML due to extraction error");
            return ublXml; // Return original on error
        }
    }

    /**
     * Convert DOM Node to XML string with proper namespace handling
     */
    private String getBusinessScopeXMl(org.w3c.dom.Node node) throws Exception {
        javax.xml.transform.TransformerFactory tf = javax.xml.transform.TransformerFactory.newInstance();
        javax.xml.transform.Transformer transformer = tf.newTransformer();
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "no");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "yes");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8");

        java.io.StringWriter writer = new java.io.StringWriter();
        transformer.transform(new javax.xml.transform.dom.DOMSource(node), new javax.xml.transform.stream.StreamResult(writer));

        String result = writer.toString();

        // Apply namespace fixing if needed
        result = fixNamespacesAndStructure(result);

        logger.debug("🔧 Converted node to XML string, length: {}", result.length());
        logger.debug("🔍 XML preview: {}", result.substring(0, Math.min(500, result.length())));

        return result;
    }

    /**
     * Fix namespace prefixes and XML structure for UBL documents
     */
    private String fixNamespacesAndStructure(String xml) {
        try {
            // Apply namespace fixes similar to NamespaceFixer
            String fixedXml = xml
                // Ensure proper UBL namespace declarations
                .replaceFirst("xmlns(:ns4)?=\"urn:oasis:names:specification:ubl:schema:xsd:Invoice-2\"",
                              "xmlns=\"urn:oasis:names:specification:ubl:schema:xsd:Invoice-2\"")
                .replaceFirst("xmlns:ns3=\"urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2\"",
                              "xmlns:cac=\"urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2\"")
                .replaceFirst("xmlns:ns2=\"urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2\"",
                              "xmlns:cec=\"urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2\"")
                .replaceFirst("xmlns(:ns1)?=\"urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2\"",
                              "xmlns:cbc=\"urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2\"")

                // Replace element prefixes in the body
                .replaceAll("<ns4:", "<")  // Default tag prefix (Invoice)
                .replaceAll("</ns4:", "</")
                .replaceAll("<ns3:", "<cac:")
                .replaceAll("</ns3:", "</cac:")
                .replaceAll("<ns2:", "<cec:")
                .replaceAll("</ns2:", "</cec:")
                .replaceAll("<ns1:", "<cbc:")
                .replaceAll("</ns1:", "</cbc:");

            // Add missing namespace declarations if not present
            if (!fixedXml.contains("xmlns:cac=") && fixedXml.contains("<cac:")) {
                fixedXml = fixedXml.replaceFirst("<Invoice",
                    "<Invoice xmlns:cac=\"urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2\"");
            }
            if (!fixedXml.contains("xmlns:cbc=") && fixedXml.contains("<cbc:")) {
                fixedXml = fixedXml.replaceFirst("<Invoice",
                    "<Invoice xmlns:cbc=\"urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2\"");
            }
            if (!fixedXml.contains("xmlns:cec=") && fixedXml.contains("<cec:")) {
                fixedXml = fixedXml.replaceFirst("<Invoice",
                    "<Invoice xmlns:cec=\"urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2\"");
            }

            return fixedXml;

        } catch (Exception e) {
            logger.warn("⚠️ Error fixing namespaces, returning original XML: {}", e.getMessage());
            return xml;
        }
    }

    /**
     * Debug helper method to list child elements of a node
     */
    private void listChildElements(org.w3c.dom.Node node, int depth) {
        if (depth > 3) return; // Limit recursion depth

        String indent = "  ".repeat(depth);
        if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
            logger.debug("{}🔍 Element: {} (namespace: {})",
                indent, node.getLocalName(), node.getNamespaceURI());
        }

        org.w3c.dom.NodeList children = node.getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            org.w3c.dom.Node child = children.item(i);
            if (child.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
                listChildElements(child, depth + 1);
            }
        }
    }

    private String extractCountryCodeFromXml(String ublXml) {
        try {
            logger.debug("Extracting country code from UBL XML");

            javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
            org.w3c.dom.Document doc = builder.parse(new java.io.ByteArrayInputStream(ublXml.getBytes(StandardCharsets.UTF_8)));

            // Look for country code in postal address elements
            org.w3c.dom.NodeList countryElements = doc.getElementsByTagName("IdentificationCode");
            for (int i = 0; i < countryElements.getLength(); i++) {
                org.w3c.dom.Element countryElement = (org.w3c.dom.Element) countryElements.item(i);
                // Check if this is within a Country element
                if (countryElement.getParentNode() != null &&
                        countryElement.getParentNode().getNodeName().contains("Country")) {
                    String countryCode = countryElement.getTextContent();
                    if (countryCode != null && !countryCode.trim().isEmpty()) {
                        logger.info("🌍 Found country code in address: {}", countryCode);
                        return countryCode.trim();
                    }
                }
            }

            logger.warn("⚠️ No country code found in UBL XML, using DEFAULT");
            return "DEFAULT";

        } catch (Exception e) {
            logger.error("❌ Error extracting country code from UBL XML: {}", e.getMessage());
            return "DEFAULT";
        }
    }

    /**
     * Extract document type from UBL XML root element
     */
    private String extractDocumentTypeFromXml(String ublXml) {
        try {
            logger.debug("Extracting document type from UBL XML");

            javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
            org.w3c.dom.Document doc = builder.parse(new java.io.ByteArrayInputStream(ublXml.getBytes(StandardCharsets.UTF_8)));

            String rootElementName = doc.getDocumentElement().getLocalName();
            if (rootElementName == null) {
                rootElementName = doc.getDocumentElement().getNodeName();
            }

            // Map root element names to document types
            String documentType;
            switch (rootElementName.toLowerCase()) {
                case "invoice":
                    documentType = "INVOICE";
                    break;
                case "creditnote":
                    documentType = "CREDITNOTE";
                    break;
                case "applicationresponse":
                    documentType = "APPLICATIONRESPONSE";
                    break;
                default:
                    logger.warn("⚠️ Unknown document type: {}, defaulting to INVOICE", rootElementName);
                    documentType = "INVOICE";
                    break;
            }

            logger.info("📄 Detected document type: {} from root element: {}", documentType, rootElementName);
            return documentType;

        } catch (Exception e) {
            logger.error("❌ Error extracting document type from UBL XML: {}", e.getMessage());
            return "INVOICE"; // Default fallback
        }
    }

    /**
     * Extract SBDH information for MLS processing
     */
    private SbdhInfo extractSbdhInfo(String ublXml) {
        try {
            logger.debug("Extracting SBDH information for MLS processing");

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(ublXml.getBytes(StandardCharsets.UTF_8)));

            String rootElementName = doc.getDocumentElement().getLocalName();
            if (rootElementName == null) {
                rootElementName = doc.getDocumentElement().getNodeName();
            }

            if (!"StandardBusinessDocument".equals(rootElementName)) {
                logger.debug("No SBDH wrapper found, skipping SBDH extraction");
                return null;
            }

            XPathFactory xPathFactory = XPathFactory.newInstance();
            XPath xpath = xPathFactory.newXPath();

            // Register namespace prefix "sh"
            NamespaceContext nsContext = new NamespaceContext() {
                public String getNamespaceURI(String prefix) {
                    if ("sh".equals(prefix)) {
                        return "http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader";
                    }
                    return XMLConstants.NULL_NS_URI;
                }

                public String getPrefix(String namespaceURI) {
                    throw new UnsupportedOperationException();
                }

                public Iterator<String> getPrefixes(String namespaceURI) {
                    throw new UnsupportedOperationException();
                }
            };
            xpath.setNamespaceContext(nsContext);

            SbdhInfo sbdhInfo = new SbdhInfo();

            // Extract InstanceIdentifier
            String instanceId = xpath.evaluate("//sh:InstanceIdentifier", doc);
            if (instanceId != null && !instanceId.isEmpty()) {
                sbdhInfo.setDocumentInstanceId(instanceId);
                logger.debug("📋 Extracted document instance ID: {}", instanceId);
            }

            // Extract BusinessScope node as string
            Node businessScopeNode = (Node) xpath.evaluate("//sh:BusinessScope", doc, XPathConstants.NODE);
            if (businessScopeNode != null) {
                String businessScopeXml = getBusinessScopeXMl(businessScopeNode);
                String cleanedBusinessScope = cleanBusinessScopeXml(businessScopeXml);
                sbdhInfo.setBusinessScope(cleanedBusinessScope);
                logger.info("📋 Extracted and cleaned COMPLETE BusinessScope section for MLS preservation, length: {}", cleanedBusinessScope.length());
                logger.debug("📋 BusinessScope content: {}", cleanedBusinessScope);
            } else {
                logger.warn("⚠️ No BusinessScope found in SBDH - MLS will use default BusinessScope");
            }

            return sbdhInfo;

        } catch (Exception e) {
            logger.error("❌ Error extracting SBDH information", e);
            return null;
        }
    }
    private String nodeToString(Node node) throws TransformerException {
        Transformer transformer =
                TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(node), new StreamResult(writer));
        return writer.toString();
    }

    /**
     * Clean BusinessScope XML to remove invalid XML declarations and fix namespaces
     */
    private String cleanBusinessScopeXml(String businessScopeXml) {
        try {
            // Remove any XML declarations that might be embedded
            String cleaned = businessScopeXml.replaceAll("<\\?xml[^>]*\\?>", "");

            // Fix namespace issues - remove sh: prefixes and xmlns declarations that cause conflicts
            cleaned = cleaned.replaceAll("sh:", "");
            cleaned = cleaned.replaceAll("xmlns:sh=\"[^\"]*\"", "");
            cleaned = cleaned.replaceAll("xmlns=\"[^\"]*\"", "");

            // Ensure proper formatting
            cleaned = cleaned.trim();

            logger.debug("📋 Cleaned BusinessScope XML: {}", cleaned);
            return cleaned;

        } catch (Exception e) {
            logger.warn("⚠️ Error cleaning BusinessScope XML, returning original: {}", e.getMessage());
            return businessScopeXml;
        }
    }



    /**
     * Inner class to hold SBDH information
     */
    static class SbdhInfo {
        private String documentInstanceId;
        private String businessScope;

        public String getDocumentInstanceId() {
            return documentInstanceId;
        }

        public void setDocumentInstanceId(String documentInstanceId) {
            this.documentInstanceId = documentInstanceId;
        }

        public String getBusinessScope() {
            return businessScope;
        }

        public void setBusinessScope(String businessScope) {
            this.businessScope = businessScope;
        }
    }
}