package com.morohub.apsp.core.service;

import com.morohub.apsp.config.CountryConfigurationService;
import jakarta.xml.bind.JAXBElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;


/**
 * Service for generating XML documents using dynamic class loading
 */
@Service
public class XmlGeneratorService {

    private static final Logger logger = LoggerFactory.getLogger(XmlGeneratorService.class);

    @Autowired
    private Environment env;

    @Autowired
    private XmlJsonConversionService xmlJsonConversionService;

    @Autowired
    private CountryConfigurationService countryConfigurationService;

     /**
     * Generate UBL Invoice XML using country-specific configuration
     */
    public <T> String generateInvoiceWithConfig(Object invoice, String countryCode, String documentType) throws Exception {
        try {
            logger.debug("Generating invoice XML using country-specific configuration: {}/{}", countryCode, documentType);

            // Get country-specific class configuration
            Class<T> invoiceClass = (Class<T>) countryConfigurationService.getUBLClass(countryCode, documentType);
            Class<?> factoryClass = countryConfigurationService.getFactoryClass(countryCode, documentType);

            logger.info("Using classes for {}/{}: {} and {}",
                countryCode, documentType, invoiceClass.getName(), factoryClass.getName());

            if (!invoiceClass.isInstance(invoice)) {
                throw new IllegalArgumentException("Invoice object is not of type " + invoiceClass.getName());
            }

            T typedInvoice = invoiceClass.cast(invoice);
            Object factoryInstance = factoryClass.getDeclaredConstructor().newInstance();

            // Determine the factory method name based on document type
            String methodName = getFactoryMethodName(documentType);
            java.lang.reflect.Method createMethod = factoryClass.getMethod(methodName, invoiceClass);
            JAXBElement<T> jaxbElement = (JAXBElement<T>) createMethod.invoke(factoryInstance, typedInvoice);

            String xml = xmlJsonConversionService.objectToXml(jaxbElement, invoiceClass);
            logger.info("Successfully generated XML for {}/{}", countryCode, documentType);
            return xml;

        } catch (Exception e) {
            logger.error("Failed to generate invoice XML for {}/{}", countryCode, documentType, e);
            throw new RuntimeException("Country-specific invoice XML generation failed: " + e.getMessage(), e);
        }
    }

    /**
     * Get the appropriate factory method name based on document type
     */
    private String getFactoryMethodName(String documentType) {
        switch (documentType.toUpperCase()) {
            case "INVOICE":
                return "createInvoice";
            case "CREDITNOTE":
                return "createCreditNote";
            case "APPLICATIONRESPONSE":
                return "createApplicationResponse";
            default:
                throw new IllegalArgumentException("Unsupported document type: " + documentType);
        }
    }


}
