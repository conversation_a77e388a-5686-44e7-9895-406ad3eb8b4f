-- Error Management System Database Schema
-- Creates tables for comprehensive error management as per requirements

-- Error Master Data Table
-- Requirement 1: Master data for all errors should be maintained in Access Point database
CREATE TABLE IF NOT EXISTS error_master_data (
    id BIGSERIAL PRIMARY KEY,
    error_code VARCHAR(50) UNIQUE NOT NULL,
    http_status_code INTEGER NOT NULL,
    error_type VARCHAR(50) NOT NULL,
    error_description TEXT NOT NULL,
    retry_required BOOLEAN NOT NULL DEFAULT FALSE,
    retry_interval_ms BIGINT,
    max_retry_attempts INTEGER,
    active BOOLEAN DEFAULT TRUE,
    category VARCHAR(100),
    severity VARCHAR(20),
    created_date TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- Error Log Table
-- For tracking error occurrences and retry attempts
CREATE TABLE IF NOT EXISTS error_log (
    id BIGSERIAL PRIMARY KEY,
    error_master_data_id BIGINT NOT NULL,
    message_id VARCHAR(255),
    request_id VARCHAR(255),
    session_id VARCHAR(255),
    user_id VARCHAR(100),
    api_endpoint VARCHAR(500),
    http_method VARCHAR(10),
    error_message TEXT,
    stack_trace TEXT,
    context_data TEXT,
    client_ip VARCHAR(45),
    user_agent VARCHAR(500),
    retry_attempt INTEGER DEFAULT 0,
    resolved BOOLEAN DEFAULT FALSE,
    resolution_notes TEXT,
    resolved_by VARCHAR(100),
    resolved_date TIMESTAMP,
    occurred_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processing_status VARCHAR(30) DEFAULT 'NEW',
    next_retry_at TIMESTAMP,
    FOREIGN KEY (error_master_data_id) REFERENCES error_master_data(id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_error_master_data_error_code ON error_master_data(error_code);
CREATE INDEX IF NOT EXISTS idx_error_master_data_error_type ON error_master_data(error_type);
CREATE INDEX IF NOT EXISTS idx_error_master_data_active ON error_master_data(active);
CREATE INDEX IF NOT EXISTS idx_error_master_data_retry_required ON error_master_data(retry_required);

CREATE INDEX IF NOT EXISTS idx_error_log_message_id ON error_log(message_id);
CREATE INDEX IF NOT EXISTS idx_error_log_request_id ON error_log(request_id);
CREATE INDEX IF NOT EXISTS idx_error_log_error_master_data_id ON error_log(error_master_data_id);
CREATE INDEX IF NOT EXISTS idx_error_log_resolved ON error_log(resolved);
CREATE INDEX IF NOT EXISTS idx_error_log_processing_status ON error_log(processing_status);
CREATE INDEX IF NOT EXISTS idx_error_log_occurred_at ON error_log(occurred_at);
CREATE INDEX IF NOT EXISTS idx_error_log_next_retry_at ON error_log(next_retry_at);

-- Comments for documentation
COMMENT ON TABLE error_master_data IS 'Master data for all error types in the Access Point system';
COMMENT ON COLUMN error_master_data.error_code IS 'Unique error code (HTTP error code, Data error code as defined by Xelerate etc.)';
COMMENT ON COLUMN error_master_data.http_status_code IS 'HTTP status code associated with this error';
COMMENT ON COLUMN error_master_data.error_type IS 'Error type (Technical Connectivity Error, Schema Validation Failure, etc.)';
COMMENT ON COLUMN error_master_data.error_description IS 'Detailed error description';
COMMENT ON COLUMN error_master_data.retry_required IS 'Whether this error requires retry (Y/N)';
COMMENT ON COLUMN error_master_data.retry_interval_ms IS 'Retry interval in milliseconds (mandatory if retry_required is true)';
COMMENT ON COLUMN error_master_data.max_retry_attempts IS 'Maximum number of retry attempts';

COMMENT ON TABLE error_log IS 'Log of error occurrences for tracking and monitoring';
COMMENT ON COLUMN error_log.error_master_data_id IS 'Reference to error master data';
COMMENT ON COLUMN error_log.message_id IS 'Message ID associated with this error (if applicable)';
COMMENT ON COLUMN error_log.request_id IS 'Request ID for tracking API requests';
COMMENT ON COLUMN error_log.retry_attempt IS 'Current retry attempt number';
COMMENT ON COLUMN error_log.processing_status IS 'Processing status for retry mechanism';
COMMENT ON COLUMN error_log.next_retry_at IS 'Next retry attempt time';

-- Update message_processing_queue table for flow-specific retry support
ALTER TABLE message_processing_queue ADD COLUMN IF NOT EXISTS flow_type VARCHAR(30);
ALTER TABLE message_processing_queue ADD COLUMN IF NOT EXISTS request_id VARCHAR(255);
ALTER TABLE message_processing_queue ADD COLUMN IF NOT EXISTS endpoint_url VARCHAR(500);
ALTER TABLE message_processing_queue ADD COLUMN IF NOT EXISTS validation_errors TEXT;
ALTER TABLE message_processing_queue ADD COLUMN IF NOT EXISTS processing_notes TEXT;

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_message_processing_queue_flow_type ON message_processing_queue(flow_type);
CREATE INDEX IF NOT EXISTS idx_message_processing_queue_request_id ON message_processing_queue(request_id);
CREATE INDEX IF NOT EXISTS idx_message_processing_queue_status_flow_type ON message_processing_queue(status, flow_type);

-- Comments for new columns
COMMENT ON COLUMN message_processing_queue.flow_type IS 'Type of processing flow (FORWARD_FLOW, REVERSE_FLOW, XML_VALIDATION, PEPPOL_SBD_INVOICE, MLS_MESSAGE)';
COMMENT ON COLUMN message_processing_queue.request_id IS 'Request ID for tracking API requests';
COMMENT ON COLUMN message_processing_queue.endpoint_url IS 'Target endpoint URL for the message';
COMMENT ON COLUMN message_processing_queue.validation_errors IS 'Validation errors encountered during processing';
COMMENT ON COLUMN message_processing_queue.processing_notes IS 'Additional notes about processing status and retry attempts';
